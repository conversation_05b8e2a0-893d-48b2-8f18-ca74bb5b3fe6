@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom PWA styles */
@layer base {
  body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  }
}

/* Utility classes for line clamping */
@layer utilities {
  .line-clamp-2 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
  }
  
  .line-clamp-3 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
  }
}

/* PWA installation styles */
.install-prompt {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* Smooth animations */
* {
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out;
}

/* Focus states */
*:focus {
  outline: none;
}

button:focus, input:focus, select:focus, textarea:focus {
  ring: 2;
  ring-color: #3B82F6;
  ring-opacity: 50;
}

/* Loading animations */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}