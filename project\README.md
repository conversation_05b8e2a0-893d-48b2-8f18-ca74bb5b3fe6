# Civic Issue Reporting PWA

A comprehensive Progressive Web Application for civic issue reporting with offline support, real-time updates, and modern UI.

## Features

### For Citizens
- ✅ Report issues with photos and location tagging
- ✅ Track report status in real-time
- ✅ Offline support - create reports without internet
- ✅ Community page to view all reported issues
- ✅ Review completed issues
- ✅ Push notifications for status updates

### For Staff
- ✅ Receive task assignments
- ✅ Update issue status
- ✅ Real-time notifications

### For Admins
- ✅ Interactive dashboard with maps
- ✅ User and staff management
- ✅ Task assignment workflow
- ✅ Analytics and insights
- ✅ Real-time monitoring

## Technology Stack

### Frontend (PWA)
- React 18 with TypeScript
- Tailwind CSS for modern UI
- PWA features (manifest, service worker)
- Offline support and background sync
- Web Push API for notifications
- HTML5 Geolocation API
- OpenStreetMap integration

### Backend
- Flask with Python
- MongoDB for data storage
- Flask-SocketIO for real-time updates
- JWT authentication
- SMTP email notifications
- GridFS for file uploads

## Prerequisites

- Node.js 18+ and npm
- Python 3.8+
- MongoDB (running on localhost:27017)

## Installation

### 1. <PERSON><PERSON> and Setup Frontend

```bash
cd frontend
npm install
```

### 2. Setup Backend

```bash
cd backend
python -m venv venv

# Windows
venv\Scripts\activate

# macOS/Linux
source venv/bin/activate

pip install -r requirements.txt
```

### 3. Environment Configuration

Create `.env` files in both frontend and backend directories with the provided templates.

## Running the Application

### Start Backend (Terminal 1)
```bash
cd backend
python app.py
```

### Start Frontend (Terminal 2)
```bash
cd frontend
npm run dev
```

## PWA Features

### Browser Mode
- Responsive web application
- Works in any modern browser
- Full functionality online and offline

### Installed Mode
- Install via browser's "Install App" prompt
- Standalone fullscreen experience
- App icon on device home screen
- Splash screen on startup
- Push notifications support

## API Endpoints

### Authentication
- `POST /api/auth/register` - User registration
- `POST /api/auth/login` - User login
- `POST /api/auth/logout` - User logout

### Issues
- `GET /api/issues` - Get all issues
- `POST /api/issues` - Create new issue
- `PUT /api/issues/:id` - Update issue
- `DELETE /api/issues/:id` - Delete issue

### Admin
- `GET /api/admin/users` - Get all users
- `POST /api/admin/assign` - Assign task to staff
- `GET /api/admin/analytics` - Get analytics data

## Database Collections

- `users` - User accounts (citizens, staff, admins)
- `issues` - Reported issues
- `notifications` - Notification history
- `reviews` - Issue reviews and ratings
- `fs.files` & `fs.chunks` - GridFS file storage

## Contributing

1. Fork the repository
2. Create your feature branch
3. Commit your changes
4. Push to the branch
5. Create a Pull Request

## License

MIT License - see LICENSE file for details