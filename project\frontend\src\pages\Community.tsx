import React, { useEffect, useState } from 'react'
import { MapPin, Calendar, User, Filter, Search } from 'lucide-react'

interface Issue {
  _id: string
  title: string
  description: string
  category: string
  status: 'submitted' | 'acknowledged' | 'in_progress' | 'resolved'
  location: {
    address: string
    coordinates: [number, number]
  }
  createdAt: string
  photo?: string
  reporter: string
  rating?: number
  review?: string
}

const Community = () => {
  const [issues, setIssues] = useState<Issue[]>([])
  const [filteredIssues, setFilteredIssues] = useState<Issue[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [categoryFilter, setCategoryFilter] = useState('')
  const [statusFilter, setStatusFilter] = useState('')

  const categories = [
    'Road Maintenance',
    'Street Lighting', 
    'Waste Management',
    'Water & Sewage',
    'Parks & Recreation',
    'Traffic & Parking',
    'Noise Pollution',
    'Building Issues',
    'Other'
  ]

  const statuses = [
    { value: 'submitted', label: 'Submitted' },
    { value: 'acknowledged', label: 'Acknowledged' },
    { value: 'in_progress', label: 'In Progress' },
    { value: 'resolved', label: 'Resolved' }
  ]

  useEffect(() => {
    const fetchIssues = async () => {
      try {
        const response = await fetch('/api/issues/community')
        const data = await response.json()
        
        if (data.issues) {
          setIssues(data.issues)
          setFilteredIssues(data.issues)
        }
      } catch (error) {
        console.error('Failed to fetch community issues:', error)
      } finally {
        setLoading(false)
      }
    }

    fetchIssues()
  }, [])

  useEffect(() => {
    let filtered = issues

    if (searchTerm) {
      filtered = filtered.filter(issue =>
        issue.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        issue.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
        issue.location.address.toLowerCase().includes(searchTerm.toLowerCase())
      )
    }

    if (categoryFilter) {
      filtered = filtered.filter(issue => issue.category === categoryFilter)
    }

    if (statusFilter) {
      filtered = filtered.filter(issue => issue.status === statusFilter)
    }

    setFilteredIssues(filtered)
  }, [issues, searchTerm, categoryFilter, statusFilter])

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'submitted':
      case 'acknowledged':
        return 'bg-yellow-100 text-yellow-800'
      case 'in_progress':
        return 'bg-blue-100 text-blue-800'
      case 'resolved':
        return 'bg-green-100 text-green-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div className="max-w-7xl mx-auto px-4 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">
          Community Issues
        </h1>
        <p className="text-gray-600">
          View all reported issues in your community and track their progress.
        </p>
      </div>

      {/* Filters */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <input
              type="text"
              placeholder="Search issues..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
          </div>

          <div className="relative">
            <Filter className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <select
              value={categoryFilter}
              onChange={(e) => setCategoryFilter(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="">All Categories</option>
              {categories.map(category => (
                <option key={category} value={category}>{category}</option>
              ))}
            </select>
          </div>

          <div>
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="">All Statuses</option>
              {statuses.map(status => (
                <option key={status.value} value={status.value}>{status.label}</option>
              ))}
            </select>
          </div>

          <div className="text-sm text-gray-600 flex items-center">
            Showing {filteredIssues.length} of {issues.length} issues
          </div>
        </div>
      </div>

      {/* Issues Grid */}
      {filteredIssues.length === 0 ? (
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-8 text-center">
          <MapPin className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No issues found</h3>
          <p className="text-gray-600">
            {searchTerm || categoryFilter || statusFilter 
              ? 'Try adjusting your search filters.' 
              : 'No issues have been reported in your community yet.'
            }
          </p>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredIssues.map((issue) => (
            <div key={issue._id} className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-shadow">
              {issue.photo && (
                <div className="aspect-video overflow-hidden">
                  <img
                    src={issue.photo}
                    alt={issue.title}
                    className="w-full h-full object-cover"
                  />
                </div>
              )}
              
              <div className="p-6">
                <div className="flex items-start justify-between mb-3">
                  <h3 className="text-lg font-semibold text-gray-900 line-clamp-2">
                    {issue.title}
                  </h3>
                  <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ml-2 ${getStatusColor(issue.status)}`}>
                    {issue.status === 'in_progress' ? 'In Progress' : issue.status.charAt(0).toUpperCase() + issue.status.slice(1)}
                  </span>
                </div>
                
                <p className="text-gray-600 text-sm mb-4 line-clamp-3">
                  {issue.description}
                </p>
                
                <div className="space-y-2 text-sm text-gray-500">
                  <div className="flex items-center">
                    <MapPin className="h-4 w-4 mr-2" />
                    <span className="truncate">{issue.location.address}</span>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <Calendar className="h-4 w-4 mr-2" />
                      <span>{new Date(issue.createdAt).toLocaleDateString()}</span>
                    </div>
                    
                    <div className="px-2 py-1 bg-gray-100 rounded text-xs">
                      {issue.category}
                    </div>
                  </div>
                  
                  <div className="flex items-center">
                    <User className="h-4 w-4 mr-2" />
                    <span>Reported by {issue.reporter}</span>
                  </div>
                </div>

                {issue.status === 'resolved' && issue.rating && (
                  <div className="mt-4 p-3 bg-green-50 rounded-lg">
                    <div className="flex items-center text-sm text-green-800">
                      <span className="font-medium">Resolution Rating: {issue.rating}/5</span>
                    </div>
                    {issue.review && (
                      <p className="text-sm text-green-700 mt-1 italic">
                        "{issue.review}"
                      </p>
                    )}
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  )
}

export default Community