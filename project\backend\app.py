import os
import io
import base64
from datetime import datetime, timedelta, timezone
from bson import ObjectId
from flask import Flask, request, jsonify, send_file
from flask_cors import CORS
from flask_socketio import SocketIO, emit, join_room, leave_room
from pymongo import MongoClient
from pymongo.errors import DuplicateKeyError
import bcrypt
import jwt
from PIL import Image
import json
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIME<PERSON>ultipart
import requests
from functools import wraps
import gridfs

# Initialize Flask app
app = Flask(__name__)
app.config['SECRET_KEY'] = os.environ.get('SECRET_KEY', 'your-secret-key-here')
app.config['UPLOAD_FOLDER'] = 'uploads'

# Enable CORS for all routes
CORS(app, origins=['http://localhost:3000'])

# Initialize SocketIO with CORS
socketio = SocketIO(app, cors_allowed_origins="http://localhost:3000")

# MongoDB connection
MONGODB_URI = os.environ.get('MONGODB_URI', 'mongodb://localhost:27017')
client = MongoClient(MONGODB_URI)
db = client['civic_reporting']

# Collections
users_collection = db['users']
issues_collection = db['issues']
notifications_collection = db['notifications']
reviews_collection = db['reviews']

# GridFS for file storage
fs = gridfs.GridFS(db)

# Email configuration (using a mock SMTP for demo)
EMAIL_HOST = 'smtp.gmail.com'
EMAIL_PORT = 587
EMAIL_USER = os.environ.get('EMAIL_USER', '<EMAIL>')
EMAIL_PASS = os.environ.get('EMAIL_PASS', 'demo_password')

# Create indexes for better performance
users_collection.create_index('email', unique=True)
issues_collection.create_index([('location.coordinates', '2dsphere')])
issues_collection.create_index('createdAt')
issues_collection.create_index('status')

def create_default_users():
    """Create default users for demo purposes"""
    default_users = [
        {
            'name': 'Admin User',
            'email': '<EMAIL>',
            'password': 'password',
            'role': 'admin'
        },
        {
            'name': 'Staff Member',
            'email': '<EMAIL>', 
            'password': 'password',
            'role': 'staff'
        },
        {
            'name': 'John Citizen',
            'email': '<EMAIL>',
            'password': 'password',
            'role': 'citizen'
        }
    ]
    
    for user_data in default_users:
        try:
            # Hash password
            hashed_password = bcrypt.hashpw(user_data['password'].encode('utf-8'), bcrypt.gensalt())
            
            user = {
                'name': user_data['name'],
                'email': user_data['email'],
                'password': hashed_password,
                'role': user_data['role'],
                'createdAt': datetime.now(timezone.utc),
                'lastLogin': None
            }
            
            users_collection.insert_one(user)
            print(f"Created default {user_data['role']}: {user_data['email']}")
        except DuplicateKeyError:
            print(f"User {user_data['email']} already exists")

# Authentication decorator
def token_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        token = request.headers.get('Authorization')
        
        if not token:
            return jsonify({'message': 'No token provided'}), 401
            
        if token.startswith('Bearer '):
            token = token[7:]
        
        try:
            data = jwt.decode(token, app.config['SECRET_KEY'], algorithms=['HS256'])
            current_user = users_collection.find_one({'_id': ObjectId(data['user_id'])})
            
            if not current_user:
                return jsonify({'message': 'Invalid token'}), 401
                
        except jwt.ExpiredSignatureError:
            return jsonify({'message': 'Token expired'}), 401
        except jwt.InvalidTokenError:
            return jsonify({'message': 'Invalid token'}), 401
            
        return f(current_user, *args, **kwargs)
    return decorated_function

def admin_required(f):
    @wraps(f)
    @token_required
    def decorated_function(current_user, *args, **kwargs):
        if current_user['role'] != 'admin':
            return jsonify({'message': 'Admin access required'}), 403
        return f(current_user, *args, **kwargs)
    return decorated_function

def staff_or_admin_required(f):
    @wraps(f)
    @token_required
    def decorated_function(current_user, *args, **kwargs):
        if current_user['role'] not in ['staff', 'admin']:
            return jsonify({'message': 'Staff or admin access required'}), 403
        return f(current_user, *args, **kwargs)
    return decorated_function

def send_email(to_email, subject, body):
    """Send email notification (mock implementation for demo)"""
    try:
        # In a real implementation, you would use actual SMTP
        print(f"MOCK EMAIL SENT TO: {to_email}")
        print(f"SUBJECT: {subject}")
        print(f"BODY: {body}")
        return True
    except Exception as e:
        print(f"Failed to send email: {str(e)}")
        return False

def create_notification(user_id, title, message, notification_type='info'):
    """Create a notification for a user"""
    notification = {
        'userId': ObjectId(user_id),
        'title': title,
        'message': message,
        'type': notification_type,
        'read': False,
        'createdAt': datetime.now(timezone.utc)
    }
    
    result = notifications_collection.insert_one(notification)
    notification['_id'] = str(result.inserted_id)
    notification['userId'] = str(notification['userId'])
    
    # Emit real-time notification via WebSocket
    socketio.emit('notification', notification, room=str(user_id))
    
    return notification

# Auth Routes
@app.route('/api/auth/register', methods=['POST'])
def register():
    try:
        data = request.get_json()
        name = data.get('name')
        email = data.get('email', '').lower()
        password = data.get('password')
        
        if not all([name, email, password]):
            return jsonify({'message': 'All fields are required'}), 400
            
        if len(password) < 6:
            return jsonify({'message': 'Password must be at least 6 characters'}), 400
        
        # Check if user exists
        if users_collection.find_one({'email': email}):
            return jsonify({'message': 'Email already registered'}), 400
        
        # Hash password
        hashed_password = bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt())
        
        # Create user
        user = {
            'name': name,
            'email': email,
            'password': hashed_password,
            'role': 'citizen',
            'createdAt': datetime.now(timezone.utc),
            'lastLogin': datetime.now(timezone.utc)
        }
        
        result = users_collection.insert_one(user)
        
        # Generate token
        token = jwt.encode({
            'user_id': str(result.inserted_id),
            'exp': datetime.now(timezone.utc) + timedelta(days=7)
        }, app.config['SECRET_KEY'], algorithm='HS256')
        
        # Send welcome email
        send_email(
            email,
            'Welcome to Civic Issue Reporter',
            f'Hello {name}, welcome to our platform!'
        )
        
        return jsonify({
            'token': token,
            'user': {
                '_id': str(result.inserted_id),
                'name': name,
                'email': email,
                'role': 'citizen'
            }
        }), 201
        
    except Exception as e:
        return jsonify({'message': f'Registration failed: {str(e)}'}), 500

@app.route('/api/auth/login', methods=['POST'])
def login():
    try:
        data = request.get_json()
        email = data.get('email', '').lower()
        password = data.get('password')
        
        if not all([email, password]):
            return jsonify({'message': 'Email and password required'}), 400
        
        # Find user
        user = users_collection.find_one({'email': email})
        
        if not user or not bcrypt.checkpw(password.encode('utf-8'), user['password']):
            return jsonify({'message': 'Invalid email or password'}), 401
        
        # Update last login
        users_collection.update_one(
            {'_id': user['_id']},
            {'$set': {'lastLogin': datetime.now(timezone.utc)}}
        )
        
        # Generate token
        token = jwt.encode({
            'user_id': str(user['_id']),
            'exp': datetime.now(timezone.utc) + timedelta(days=7)
        }, app.config['SECRET_KEY'], algorithm='HS256')
        
        return jsonify({
            'token': token,
            'user': {
                '_id': str(user['_id']),
                'name': user['name'],
                'email': user['email'],
                'role': user['role']
            }
        }), 200
        
    except Exception as e:
        return jsonify({'message': f'Login failed: {str(e)}'}), 500

@app.route('/api/auth/verify', methods=['GET'])
@token_required
def verify_token(current_user):
    return jsonify({
        'user': {
            '_id': str(current_user['_id']),
            'name': current_user['name'],
            'email': current_user['email'],
            'role': current_user['role']
        }
    }), 200

# Issue Routes
@app.route('/api/issues', methods=['POST'])
@token_required
def create_issue(current_user):
    try:
        # Handle file upload
        photo_id = None
        if 'photo' in request.files:
            photo = request.files['photo']
            if photo.filename:
                # Resize and store image
                image = Image.open(photo.stream)
                image.thumbnail((800, 600), Image.Resampling.LANCZOS)
                
                # Convert to bytes
                img_byte_arr = io.BytesIO()
                image.save(img_byte_arr, format='JPEG')
                img_byte_arr = img_byte_arr.getvalue()
                
                # Store in GridFS
                photo_id = fs.put(img_byte_arr, filename=photo.filename, content_type='image/jpeg')
        
        # Get form data
        title = request.form.get('title')
        description = request.form.get('description') 
        category = request.form.get('category')
        location_str = request.form.get('location')
        
        if not all([title, description, category, location_str]):
            return jsonify({'message': 'All fields are required'}), 400
        
        location = json.loads(location_str)
        
        # Create issue
        issue = {
            'title': title,
            'description': description,
            'category': category,
            'status': 'submitted',
            'location': location,
            'reporterId': current_user['_id'],
            'reporter': current_user['name'],
            'createdAt': datetime.now(timezone.utc),
            'updatedAt': datetime.now(timezone.utc)
        }
        
        if photo_id:
            issue['photoId'] = photo_id
        
        result = issues_collection.insert_one(issue)
        
        # Send confirmation email
        send_email(
            current_user['email'],
            'Issue Report Submitted',
            f'Your issue "{title}" has been submitted and assigned ID: {str(result.inserted_id)}'
        )
        
        # Create notification
        create_notification(
            str(current_user['_id']),
            'Issue Submitted',
            f'Your issue "{title}" has been submitted successfully.',
            'success'
        )
        
        # Notify admins
        admin_users = users_collection.find({'role': 'admin'})
        for admin in admin_users:
            create_notification(
                str(admin['_id']),
                'New Issue Reported',
                f'A new issue "{title}" has been reported by {current_user["name"]}.',
                'info'
            )
        
        return jsonify({
            'message': 'Issue reported successfully',
            'issue_id': str(result.inserted_id)
        }), 201
        
    except Exception as e:
        return jsonify({'message': f'Failed to create issue: {str(e)}'}), 500

@app.route('/api/issues/user', methods=['GET'])
@token_required
def get_user_issues(current_user):
    try:
        issues = list(issues_collection.find({'reporterId': current_user['_id']}))
        
        # Convert ObjectIds to strings and add photo URLs
        for issue in issues:
            issue['_id'] = str(issue['_id'])
            issue['reporterId'] = str(issue['reporterId'])
            
            if 'photoId' in issue:
                issue['photo'] = f'/api/photos/{issue["photoId"]}'
                del issue['photoId']
        
        return jsonify({'issues': issues}), 200
        
    except Exception as e:
        return jsonify({'message': f'Failed to fetch issues: {str(e)}'}), 500

@app.route('/api/issues/community', methods=['GET'])
def get_community_issues():
    try:
        issues = list(issues_collection.find().sort('createdAt', -1))
        
        # Convert ObjectIds to strings and add photo URLs
        for issue in issues:
            issue['_id'] = str(issue['_id'])
            issue['reporterId'] = str(issue['reporterId'])
            
            if 'photoId' in issue:
                issue['photo'] = f'/api/photos/{issue["photoId"]}'
                del issue['photoId']
        
        return jsonify({'issues': issues}), 200
        
    except Exception as e:
        return jsonify({'message': f'Failed to fetch community issues: {str(e)}'}), 500

@app.route('/api/photos/<photo_id>', methods=['GET'])
def get_photo(photo_id):
    try:
        photo = fs.get(ObjectId(photo_id))
        return send_file(io.BytesIO(photo.read()), mimetype='image/jpeg')
    except Exception as e:
        return jsonify({'message': 'Photo not found'}), 404

# Notification Routes
@app.route('/api/notifications', methods=['GET'])
@token_required
def get_notifications(current_user):
    try:
        notifications = list(notifications_collection.find(
            {'userId': current_user['_id']}
        ).sort('createdAt', -1))
        
        # Convert ObjectIds to strings
        for notification in notifications:
            notification['_id'] = str(notification['_id'])
            notification['userId'] = str(notification['userId'])
        
        return jsonify({'notifications': notifications}), 200
        
    except Exception as e:
        return jsonify({'message': f'Failed to fetch notifications: {str(e)}'}), 500

@app.route('/api/notifications/<notification_id>/read', methods=['PUT'])
@token_required  
def mark_notification_read(current_user, notification_id):
    try:
        notifications_collection.update_one(
            {'_id': ObjectId(notification_id), 'userId': current_user['_id']},
            {'$set': {'read': True}}
        )
        
        return jsonify({'message': 'Notification marked as read'}), 200
        
    except Exception as e:
        return jsonify({'message': f'Failed to update notification: {str(e)}'}), 500

# Admin Routes
@app.route('/api/admin/users', methods=['GET'])
@admin_required
def get_all_users(current_user):
    try:
        users = list(users_collection.find({}, {'password': 0}))
        
        # Convert ObjectIds to strings
        for user in users:
            user['_id'] = str(user['_id'])
        
        return jsonify({'users': users}), 200
        
    except Exception as e:
        return jsonify({'message': f'Failed to fetch users: {str(e)}'}), 500

@app.route('/api/admin/issues', methods=['GET'])
@admin_required
def get_all_issues(current_user):
    try:
        issues = list(issues_collection.find().sort('createdAt', -1))
        
        # Convert ObjectIds to strings
        for issue in issues:
            issue['_id'] = str(issue['_id'])
            issue['reporterId'] = str(issue['reporterId'])
            
            if 'photoId' in issue:
                issue['photo'] = f'/api/photos/{issue["photoId"]}'
                del issue['photoId']
        
        return jsonify({'issues': issues}), 200
        
    except Exception as e:
        return jsonify({'message': f'Failed to fetch issues: {str(e)}'}), 500

@app.route('/api/admin/analytics', methods=['GET'])
@admin_required
def get_analytics(current_user):
    try:
        # Calculate analytics
        total_users = users_collection.count_documents({})
        total_issues = issues_collection.count_documents({})
        resolved_issues = issues_collection.count_documents({'status': 'resolved'})
        pending_issues = issues_collection.count_documents({'status': {'$in': ['submitted', 'acknowledged']}})
        
        # Category statistics
        pipeline = [
            {'$group': {'_id': '$category', 'count': {'$sum': 1}}},
            {'$sort': {'count': -1}}
        ]
        category_stats = {doc['_id']: doc['count'] for doc in issues_collection.aggregate(pipeline)}
        
        # Mock monthly reports (in real app, calculate from actual data)
        monthly_reports = [
            {'month': 'Jan 2024', 'count': 45},
            {'month': 'Feb 2024', 'count': 52},
            {'month': 'Mar 2024', 'count': 38},
            {'month': 'Apr 2024', 'count': 61},
            {'month': 'May 2024', 'count': 47},
            {'month': 'Jun 2024', 'count': 55}
        ]
        
        analytics = {
            'totalUsers': total_users,
            'totalIssues': total_issues,
            'resolvedIssues': resolved_issues,
            'pendingIssues': pending_issues,
            'categoryStats': category_stats,
            'monthlyReports': monthly_reports,
            'averageResolutionTime': 5.2,  # Mock data
            'satisfactionScore': 4.3  # Mock data
        }
        
        return jsonify({'analytics': analytics}), 200
        
    except Exception as e:
        return jsonify({'message': f'Failed to fetch analytics: {str(e)}'}), 500

@app.route('/api/admin/users/<user_id>/role', methods=['PUT'])
@admin_required
def update_user_role(current_user, user_id):
    try:
        data = request.get_json()
        new_role = data.get('role')
        
        if new_role not in ['citizen', 'staff', 'admin']:
            return jsonify({'message': 'Invalid role'}), 400
        
        result = users_collection.update_one(
            {'_id': ObjectId(user_id)},
            {'$set': {'role': new_role}}
        )
        
        if result.modified_count == 0:
            return jsonify({'message': 'User not found'}), 404
        
        # Get updated user
        updated_user = users_collection.find_one({'_id': ObjectId(user_id)})
        
        # Notify user of role change
        create_notification(
            user_id,
            'Role Updated',
            f'Your role has been updated to {new_role}.',
            'info'
        )
        
        return jsonify({'message': 'Role updated successfully'}), 200
        
    except Exception as e:
        return jsonify({'message': f'Failed to update role: {str(e)}'}), 500

@app.route('/api/admin/issues/<issue_id>/assign', methods=['PUT'])
@admin_required
def assign_issue(current_user, issue_id):
    try:
        data = request.get_json()
        staff_id = data.get('staffId')
        
        # Update issue status and assign staff
        result = issues_collection.update_one(
            {'_id': ObjectId(issue_id)},
            {
                '$set': {
                    'assignedTo': ObjectId(staff_id),
                    'status': 'acknowledged',
                    'updatedAt': datetime.now(timezone.utc)
                }
            }
        )
        
        if result.modified_count == 0:
            return jsonify({'message': 'Issue not found'}), 404
        
        # Get issue and staff details
        issue = issues_collection.find_one({'_id': ObjectId(issue_id)})
        staff = users_collection.find_one({'_id': ObjectId(staff_id)})
        
        # Notify staff member
        create_notification(
            staff_id,
            'New Assignment',
            f'You have been assigned to handle issue: "{issue["title"]}"',
            'info'
        )
        
        # Notify issue reporter
        create_notification(
            str(issue['reporterId']),
            'Issue Acknowledged',
            f'Your issue "{issue["title"]}" has been acknowledged and assigned to our staff.',
            'info'
        )
        
        return jsonify({'message': 'Issue assigned successfully'}), 200
        
    except Exception as e:
        return jsonify({'message': f'Failed to assign issue: {str(e)}'}), 500

# WebSocket events
@socketio.on('connect')
def handle_connect():
    print('Client connected')

@socketio.on('disconnect')
def handle_disconnect():
    print('Client disconnected')

@socketio.on('join')
def handle_join(data):
    user_id = data['user_id']
    join_room(user_id)
    print(f'User {user_id} joined their notification room')

@socketio.on('leave')
def handle_leave(data):
    user_id = data['user_id']
    leave_room(user_id)
    print(f'User {user_id} left their notification room')

if __name__ == '__main__':
    # Create upload directory
    os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)
    
    # Create default users on startup
    create_default_users()
    
    print("Starting Civic Issue Reporting API...")
    print("Default users created:")
    print("  Admin: <EMAIL> / password")
    print("  Staff: <EMAIL> / password")
    print("  Citizen: <EMAIL> / password")
    
    # Run the app
    socketio.run(app, debug=True, host='0.0.0.0', port=5000)