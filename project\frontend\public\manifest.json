{"name": "Civic Issue Reporter", "short_name": "CivicReport", "description": "Report and track civic issues in your community", "start_url": "/", "scope": "/", "display": "standalone", "orientation": "portrait-primary", "theme_color": "#2563eb", "background_color": "#ffffff", "lang": "en-US", "icons": [{"src": "pwa-64x64.png", "sizes": "64x64", "type": "image/png"}, {"src": "pwa-192x192.png", "sizes": "192x192", "type": "image/png"}, {"src": "pwa-512x512.png", "sizes": "512x512", "type": "image/png"}, {"src": "pwa-512x512.png", "sizes": "512x512", "type": "image/png", "purpose": "any"}, {"src": "pwa-512x512.png", "sizes": "512x512", "type": "image/png", "purpose": "maskable"}], "categories": ["social", "utilities", "government"], "shortcuts": [{"name": "Report Issue", "short_name": "Report", "description": "Quickly report a new civic issue", "url": "/report", "icons": [{"src": "pwa-192x192.png", "sizes": "192x192", "type": "image/png"}]}, {"name": "View Community Issues", "short_name": "Community", "description": "Browse issues reported by the community", "url": "/community", "icons": [{"src": "pwa-192x192.png", "sizes": "192x192", "type": "image/png"}]}], "screenshots": [{"src": "screenshot-narrow.png", "sizes": "540x720", "type": "image/png", "form_factor": "narrow"}, {"src": "screenshot-wide.png", "sizes": "720x540", "type": "image/png", "form_factor": "wide"}]}