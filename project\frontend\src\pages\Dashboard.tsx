import React, { useEffect, useState } from 'react'
import { useAuth } from '../contexts/AuthContext'
import { 
  FileText, 
  Clock, 
  CheckCircle, 
  AlertTriangle,
  MapPin,
  Calendar,
  Star
} from 'lucide-react'

interface Issue {
  _id: string
  title: string
  description: string
  category: string
  status: 'submitted' | 'acknowledged' | 'in_progress' | 'resolved'
  location: {
    address: string
    coordinates: [number, number]
  }
  createdAt: string
  updatedAt: string
  photo?: string
  rating?: number
  review?: string
}

const Dashboard = () => {
  const { user } = useAuth()
  const [issues, setIssues] = useState<Issue[]>([])
  const [loading, setLoading] = useState(true)
  const [stats, setStats] = useState({
    total: 0,
    submitted: 0,
    inProgress: 0,
    resolved: 0
  })

  useEffect(() => {
    const fetchUserIssues = async () => {
      try {
        const token = localStorage.getItem('token')
        const response = await fetch('/api/issues/user', {
          headers: { Authorization: `Bearer ${token}` }
        })
        const data = await response.json()
        
        if (data.issues) {
          setIssues(data.issues)
          
          // Calculate stats
          const stats = data.issues.reduce((acc: any, issue: Issue) => {
            acc.total++
            switch (issue.status) {
              case 'submitted':
              case 'acknowledged':
                acc.submitted++
                break
              case 'in_progress':
                acc.inProgress++
                break
              case 'resolved':
                acc.resolved++
                break
            }
            return acc
          }, { total: 0, submitted: 0, inProgress: 0, resolved: 0 })
          
          setStats(stats)
        }
      } catch (error) {
        console.error('Failed to fetch user issues:', error)
      } finally {
        setLoading(false)
      }
    }

    fetchUserIssues()
  }, [])

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'submitted':
      case 'acknowledged':
        return 'bg-yellow-100 text-yellow-800'
      case 'in_progress':
        return 'bg-blue-100 text-blue-800'
      case 'resolved':
        return 'bg-green-100 text-green-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'submitted':
      case 'acknowledged':
        return <Clock className="h-4 w-4" />
      case 'in_progress':
        return <AlertTriangle className="h-4 w-4" />
      case 'resolved':
        return <CheckCircle className="h-4 w-4" />
      default:
        return <FileText className="h-4 w-4" />
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div className="max-w-7xl mx-auto px-4 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900">
          Welcome back, {user?.name}!
        </h1>
        <p className="text-gray-600 mt-2">
          Here's an overview of your reported issues and their current status.
        </p>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center">
            <FileText className="h-8 w-8 text-gray-600" />
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Total Reports</p>
              <p className="text-2xl font-bold text-gray-900">{stats.total}</p>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center">
            <Clock className="h-8 w-8 text-yellow-600" />
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Pending</p>
              <p className="text-2xl font-bold text-yellow-600">{stats.submitted}</p>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center">
            <AlertTriangle className="h-8 w-8 text-blue-600" />
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">In Progress</p>
              <p className="text-2xl font-bold text-blue-600">{stats.inProgress}</p>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center">
            <CheckCircle className="h-8 w-8 text-green-600" />
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Resolved</p>
              <p className="text-2xl font-bold text-green-600">{stats.resolved}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Issues List */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="px-6 py-4 border-b border-gray-200">
          <h2 className="text-lg font-semibold text-gray-900">Your Reported Issues</h2>
        </div>

        {issues.length === 0 ? (
          <div className="p-8 text-center">
            <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No issues reported yet</h3>
            <p className="text-gray-600 mb-6">
              Start by reporting your first civic issue to help improve your community.
            </p>
            <a
              href="/report"
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
            >
              Report an Issue
            </a>
          </div>
        ) : (
          <div className="divide-y divide-gray-200">
            {issues.map((issue) => (
              <div key={issue._id} className="p-6 hover:bg-gray-50">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-3 mb-2">
                      <h3 className="text-lg font-medium text-gray-900">
                        {issue.title}
                      </h3>
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(issue.status)}`}>
                        {getStatusIcon(issue.status)}
                        <span className="ml-1 capitalize">
                          {issue.status === 'in_progress' ? 'In Progress' : issue.status}
                        </span>
                      </span>
                    </div>
                    
                    <p className="text-gray-600 mb-3">{issue.description}</p>
                    
                    <div className="flex items-center space-x-4 text-sm text-gray-500">
                      <div className="flex items-center">
                        <MapPin className="h-4 w-4 mr-1" />
                        {issue.location.address}
                      </div>
                      <div className="flex items-center">
                        <Calendar className="h-4 w-4 mr-1" />
                        {new Date(issue.createdAt).toLocaleDateString()}
                      </div>
                      <div className="px-2 py-1 bg-gray-100 rounded text-xs">
                        {issue.category}
                      </div>
                    </div>

                    {issue.status === 'resolved' && issue.rating && (
                      <div className="flex items-center mt-3 p-3 bg-green-50 rounded-lg">
                        <Star className="h-4 w-4 text-yellow-400 mr-1" />
                        <span className="text-sm text-green-800">
                          Rated {issue.rating}/5 stars
                        </span>
                        {issue.review && (
                          <span className="text-sm text-green-700 ml-2">
                            - "{issue.review}"
                          </span>
                        )}
                      </div>
                    )}
                  </div>

                  {issue.photo && (
                    <div className="ml-6">
                      <img
                        src={issue.photo}
                        alt="Issue photo"
                        className="w-24 h-24 object-cover rounded-lg"
                      />
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  )
}

export default Dashboard