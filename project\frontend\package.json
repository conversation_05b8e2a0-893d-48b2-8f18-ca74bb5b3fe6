{"name": "civic-issue-reporting-frontend", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite --host", "build": "vite build", "preview": "vite preview", "lint": "eslint .", "type-check": "tsc --noEmit"}, "dependencies": {"react": "^18.3.1", "react-dom": "^18.3.1", "react-router-dom": "^6.26.0", "lucide-react": "^0.344.0", "leaflet": "^1.9.4", "react-leaflet": "^4.2.1", "@types/leaflet": "^1.9.8"}, "devDependencies": {"@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.18", "eslint": "^9.9.1", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "typescript": "^5.5.3", "vite": "^5.4.2", "vite-plugin-pwa": "^0.20.1"}}